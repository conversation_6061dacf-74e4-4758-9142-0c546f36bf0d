#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票持有天数计算模块
根据股票代码、初始买入日期、预期收益率，计算达到预期收益需要持有的自然日数
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Tuple
from stock_data_loader import load_stock_data
from trading_fees import TradingFeeCalculator, TradingFeeConfig


class HoldingDaysCalculator:
    """股票持有天数计算器"""
    
    def __init__(self, data_root: str = "scheduler/scheduler_results"):
        """
        初始化持有天数计算器
        
        Args:
            data_root: 数据根目录路径
        """
        self.data_root = data_root
        self.fee_calculator = TradingFeeCalculator()
    
    def find_nearest_trading_date(self, stock_code: str, target_date: str, 
                                 search_range_days: int = 30) -> Optional[str]:
        """
        查找最近的交易日（交易日 >= 目标日期）
        
        Args:
            stock_code: 股票代码
            target_date: 目标日期，格式 'YYYY-MM-DD'
            search_range_days: 搜索范围天数
            
        Returns:
            最近的交易日，格式 'YYYY-MM-DD'，如果未找到则返回None
        """
        try:
            # 计算搜索结束日期
            target_dt = datetime.strptime(target_date, '%Y-%m-%d')
            end_dt = target_dt + timedelta(days=search_range_days)
            end_date = end_dt.strftime('%Y-%m-%d')
            
            # 获取股票数据
            df = load_stock_data(stock_code, target_date, end_date, self.data_root)
            
            if df is None or len(df) == 0:
                return None
            
            # 确保索引是日期类型
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            
            # 找到第一个 >= 目标日期的交易日
            target_dt_naive = pd.to_datetime(target_date)
            valid_dates = df.index[df.index >= target_dt_naive]
            
            if len(valid_dates) > 0:
                return valid_dates[0].strftime('%Y-%m-%d')
            
            return None
            
        except Exception as e:
            print(f"查找最近交易日时发生错误: {e}")
            return None
    
    def calculate_holding_days_for_target_return(self, stock_code: str, buy_date: str, 
                                               target_return_rate: float, lots: int = 1,
                                               max_search_days: int = 365) -> Dict:
        """
        计算达到目标收益率需要的持有天数
        
        Args:
            stock_code: 股票代码，如 '000001'
            buy_date: 买入日期，格式 'YYYY-MM-DD'
            target_return_rate: 目标收益率（百分比），如 5.0 表示5%
            lots: 买入手数（1手=100股），默认1手
            max_search_days: 最大搜索天数，默认365天
            
        Returns:
            包含计算结果的字典
        """
        try:
            print("=" * 80)
            print("持有天数计算")
            print("=" * 80)
            print(f"股票代码: {stock_code}")
            print(f"目标买入日期: {buy_date}")
            print(f"目标收益率: {target_return_rate:.4f}%")
            print(f"买入手数: {lots} 手 ({lots * 100} 股)")
            print(f"最大搜索范围: {max_search_days} 天")
            print("=" * 80)
            
            # 1. 查找最近的买入交易日
            actual_buy_date = self.find_nearest_trading_date(stock_code, buy_date)
            if not actual_buy_date:
                return {
                    'error': f'未找到股票 {stock_code} 在 {buy_date} 之后的交易日',
                    'stock_code': stock_code,
                    'target_buy_date': buy_date
                }
            
            print(f"实际买入日期: {actual_buy_date}")
            
            # 2. 获取买入日的数据
            buy_data = self._get_day_data(stock_code, actual_buy_date)
            if not buy_data:
                return {
                    'error': f'未能获取股票 {stock_code} 在 {actual_buy_date} 的数据',
                    'stock_code': stock_code,
                    'buy_date': actual_buy_date
                }
            
            buy_price = buy_data['open']  # 买入按当天开盘价
            shares = lots * 100           # 总股数
            
            print(f"买入价格: {buy_price:.3f} 元/股 (开盘价)")
            
            # 3. 计算买入费用和目标卖出价格
            buy_fees = self.fee_calculator.calculate_buy_fees(stock_code, buy_price, shares)
            total_cost = buy_fees['total_cost']  # 总成本（含买入费用）
            
            # 计算目标净收入（达到目标收益率所需的净收入）
            target_net_profit = total_cost * (target_return_rate / 100)
            target_net_revenue = total_cost + target_net_profit
            
            print(f"买入总成本: {total_cost:.2f} 元")
            print(f"目标净利润: {target_net_profit:.2f} 元")
            print(f"目标净收入: {target_net_revenue:.2f} 元")
            
            # 4. 计算目标收盘价（考虑卖出费用）
            target_close_price = self._calculate_target_close_price(
                stock_code, target_net_revenue, shares
            )
            
            print(f"目标收盘价: {target_close_price:.3f} 元/股")
            
            # 5. 获取历史数据进行搜索
            end_date = (datetime.strptime(actual_buy_date, '%Y-%m-%d') + 
                       timedelta(days=max_search_days)).strftime('%Y-%m-%d')
            
            df = load_stock_data(stock_code, actual_buy_date, end_date, self.data_root)
            
            if df is None or len(df) == 0:
                return {
                    'error': f'未能获取股票 {stock_code} 的历史数据',
                    'stock_code': stock_code,
                    'buy_date': actual_buy_date
                }
            
            # 确保索引是日期类型
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            
            # 6. 搜索达到目标收盘价的日期
            buy_date_dt = pd.to_datetime(actual_buy_date)
            future_data = df[df.index > buy_date_dt]  # 买入日之后的数据
            
            target_dates = []
            for date, row in future_data.iterrows():
                if row['close'] >= target_close_price:
                    # 计算实际收益率
                    actual_result = self._calculate_actual_profit(
                        stock_code, buy_price, row['close'], shares, 
                        actual_buy_date, date.strftime('%Y-%m-%d')
                    )
                    
                    if actual_result['profit_loss']['net_return_rate'] >= target_return_rate:
                        natural_days = (date - buy_date_dt).days
                        target_dates.append({
                            'date': date.strftime('%Y-%m-%d'),
                            'close_price': row['close'],
                            'natural_days': natural_days,
                            'actual_return_rate': actual_result['profit_loss']['net_return_rate'],
                            'net_profit': actual_result['profit_loss']['net_profit']
                        })
            
            # 7. 分析结果
            if not target_dates:
                return {
                    'error': f'在 {max_search_days} 天内未找到达到目标收益率 {target_return_rate:.4f}% 的日期',
                    'stock_code': stock_code,
                    'buy_date': actual_buy_date,
                    'target_return_rate': target_return_rate,
                    'buy_price': buy_price,
                    'target_close_price': target_close_price,
                    'search_range': f'{actual_buy_date} 到 {end_date}',
                    'max_close_price': float(future_data['close'].max()) if len(future_data) > 0 else None
                }
            
            # 找到第一个达到目标的日期
            first_target = target_dates[0]
            
            result = {
                'stock_code': stock_code,
                'target_buy_date': buy_date,
                'actual_buy_date': actual_buy_date,
                'target_return_rate': target_return_rate,
                'lots': lots,
                'shares': shares,
                
                'buy_info': {
                    'buy_price': round(buy_price, 3),
                    'total_cost': round(total_cost, 2),
                    'buy_fees': buy_fees
                },
                
                'target_info': {
                    'target_close_price': round(target_close_price, 3),
                    'target_net_profit': round(target_net_profit, 2),
                    'target_net_revenue': round(target_net_revenue, 2)
                },
                
                'result': {
                    'first_target_date': first_target['date'],
                    'natural_days_needed': first_target['natural_days'],
                    'actual_close_price': round(first_target['close_price'], 3),
                    'actual_return_rate': round(first_target['actual_return_rate'], 4),
                    'actual_net_profit': round(first_target['net_profit'], 2)
                },
                
                'all_target_dates': target_dates[:10],  # 最多返回前10个
                'total_found': len(target_dates)
            }
            
            # 8. 打印结果
            self._print_calculation_results(result)
            
            return result
            
        except Exception as e:
            return {
                'error': f'计算持有天数时发生错误: {str(e)}',
                'stock_code': stock_code,
                'target_buy_date': buy_date
            }
    
    def _calculate_target_close_price(self, stock_code: str, target_net_revenue: float, 
                                    shares: int) -> float:
        """
        计算达到目标净收入所需的收盘价（考虑卖出费用）
        使用二分法求解
        """
        # 初始估算范围
        low_price = target_net_revenue / shares * 0.8  # 下限
        high_price = target_net_revenue / shares * 1.5  # 上限
        epsilon = 0.001  # 精度
        
        max_iterations = 100
        iteration = 0
        
        while high_price - low_price > epsilon and iteration < max_iterations:
            mid_price = (low_price + high_price) / 2
            
            # 计算该价格下的卖出费用
            sell_fees = self.fee_calculator.calculate_sell_fees(stock_code, mid_price, shares)
            actual_net_revenue = sell_fees['net_amount']
            
            if actual_net_revenue < target_net_revenue:
                low_price = mid_price
            else:
                high_price = mid_price
            
            iteration += 1
        
        return (low_price + high_price) / 2
    
    def _get_day_data(self, stock_code: str, date: str) -> Optional[Dict]:
        """
        获取指定日期的股票数据
        """
        try:
            # 获取当日数据（使用前后各1天的范围以确保获取到数据）
            start_date = (datetime.strptime(date, '%Y-%m-%d') - timedelta(days=1)).strftime('%Y-%m-%d')
            end_date = (datetime.strptime(date, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d')
            
            df = load_stock_data(stock_code, start_date, end_date, self.data_root)
            
            if df is None or len(df) == 0:
                return None
            
            # 确保索引是日期类型
            if not isinstance(df.index, pd.DatetimeIndex):
                df.index = pd.to_datetime(df.index)
            
            # 查找指定日期的数据
            target_date = pd.to_datetime(date)
            
            if target_date in df.index:
                row = df.loc[target_date]
                return {
                    'date': date,
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': int(row['volume']) if 'volume' in row else 0
                }
            
            return None
            
        except Exception as e:
            print(f"获取 {date} 数据时发生错误: {e}")
            return None
    
    def _calculate_actual_profit(self, stock_code: str, buy_price: float, sell_price: float,
                               shares: int, buy_date: str, sell_date: str) -> Dict:
        """
        计算实际的收益情况
        """
        # 计算交易费用
        buy_fees = self.fee_calculator.calculate_buy_fees(stock_code, buy_price, shares)
        sell_fees = self.fee_calculator.calculate_sell_fees(stock_code, sell_price, shares)
        
        # 计算收益
        total_cost = buy_fees['total_cost']
        net_revenue = sell_fees['net_amount']
        net_profit = net_revenue - total_cost
        net_return_rate = (net_profit / total_cost) * 100 if total_cost > 0 else 0
        
        return {
            'buy_date': buy_date,
            'sell_date': sell_date,
            'buy_price': buy_price,
            'sell_price': sell_price,
            'profit_loss': {
                'net_profit': net_profit,
                'net_return_rate': net_return_rate
            }
        }
    
    def _print_calculation_results(self, result: Dict):
        """打印计算结果"""
        try:
            print("\n" + "=" * 80)
            print("📊 持有天数计算结果")
            print("=" * 80)
            
            # 基本信息
            print(f"📈 股票代码: {result['stock_code']}")
            print(f"📅 目标买入日期: {result['target_buy_date']}")
            print(f"📅 实际买入日期: {result['actual_buy_date']}")
            print(f"🎯 目标收益率: {result['target_return_rate']:.4f}%")
            print(f"📦 交易数量: {result['lots']} 手 ({result['shares']} 股)")
            
            # 买入信息
            buy_info = result['buy_info']
            print(f"\n💰 买入信息:")
            print(f"   买入价格: {buy_info['buy_price']:.3f} 元/股")
            print(f"   总成本: {buy_info['total_cost']:,.2f} 元")
            print(f"   买入费用: {buy_info['buy_fees']['total_fees']:.2f} 元")
            
            # 目标信息
            target_info = result['target_info']
            print(f"\n🎯 目标信息:")
            print(f"   目标收盘价: {target_info['target_close_price']:.3f} 元/股")
            print(f"   目标净利润: {target_info['target_net_profit']:,.2f} 元")
            print(f"   目标净收入: {target_info['target_net_revenue']:,.2f} 元")
            
            # 计算结果
            result_info = result['result']
            print(f"\n✅ 计算结果:")
            print(f"   首次达到目标日期: {result_info['first_target_date']}")
            print(f"   需要持有天数: {result_info['natural_days_needed']} 个自然日")
            print(f"   实际收盘价: {result_info['actual_close_price']:.3f} 元/股")
            print(f"   实际收益率: {result_info['actual_return_rate']:.4f}%")
            print(f"   实际净利润: {result_info['actual_net_profit']:,.2f} 元")
            
            # 统计信息
            print(f"\n📊 统计信息:")
            print(f"   找到 {result['total_found']} 个符合条件的日期")
            
            if len(result['all_target_dates']) > 1:
                print(f"   前5个达标日期:")
                for i, target in enumerate(result['all_target_dates'][:5], 1):
                    print(f"     {i}. {target['date']} (第{target['natural_days']}天): "
                          f"收盘价 {target['close_price']:.3f}, "
                          f"收益率 {target['actual_return_rate']:.4f}%")
            
            print("\n" + "=" * 80)
            
        except Exception as e:
            print(f"打印计算结果时发生错误: {e}")


def calculate_holding_days_for_target(stock_code: str, buy_date: str, target_return_rate: float,
                                    lots: int = 1, max_search_days: int = 365,
                                    data_root: str = "scheduler/scheduler_results") -> Dict:
    """
    便捷函数：计算达到目标收益率需要的持有天数
    
    Args:
        stock_code: 股票代码，如 '000001'
        buy_date: 买入日期，格式 'YYYY-MM-DD'
        target_return_rate: 目标收益率（百分比），如 5.0 表示5%
        lots: 买入手数（1手=100股），默认1手
        max_search_days: 最大搜索天数，默认365天
        data_root: 数据根目录路径
        
    Returns:
        包含计算结果的字典
    """
    calculator = HoldingDaysCalculator(data_root)
    return calculator.calculate_holding_days_for_target_return(
        stock_code, buy_date, target_return_rate, lots, max_search_days
    )


def main():
    """主函数：演示持有天数计算"""
    
    # 示例参数
    stock_code = "000002"        # 平安银行
    buy_date = "2025-07-23"      # 买入日期
    target_return_rate = 5.0     # 目标收益率5%
    lots = 1                     # 购买1手（100股）
    max_search_days = 365        # 最大搜索180天
    
    print("🚀 股票持有天数计算演示")
    print(f"📊 股票代码: {stock_code}")
    print(f"📅 目标买入日期: {buy_date}")
    print(f"🎯 目标收益率: {target_return_rate}%")
    print(f"📦 购买手数: {lots} 手")
    print(f"🔍 最大搜索范围: {max_search_days} 天")
    
    # 执行计算
    result = calculate_holding_days_for_target(
        stock_code=stock_code,
        buy_date=buy_date,
        target_return_rate=target_return_rate,
        lots=lots,
        max_search_days=max_search_days
    )
    
    if result and 'error' not in result:
        print("\n✅ 持有天数计算完成！")
        
        # 保存结果到文件
        import json
        output_file = f"holding_days_{stock_code}_{result['actual_buy_date']}_{target_return_rate}percent.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"📄 计算结果已保存到: {output_file}")
        
    else:
        error_msg = result.get('error', '未知错误') if result else '计算失败'
        print(f"\n❌ 计算失败: {error_msg}")
    
    return result


if __name__ == "__main__":
    result = main()